'use client'

import { usePathname } from 'next/navigation'
import isHomepage from '@/lib/isHomepage'
import { ReactNode } from 'react'
import { PageFolder, Feedback, Project } from '@/app/types/types'
import CarouselSection from '@/app/components/CarouselSection'
import { cn } from '@/lib/utils'
import Breadcrumb from '@/app/components/Breadcrumb'

type Props = {
  children: ReactNode
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const GridLayout = ({ children, services, clientFeedback, projects }: Props) => {
  const pathname = usePathname()
  const isHome = isHomepage(pathname)

  return (
    <div
      className={cn(
        isHome ? 'grid-areas-layout-home-mobile *:max-xl:contents' : 'grid-areas-layout-mobile',
        'grid flex-1',
        'grid-cols-layout-mobile xl:grid-cols-layout xl:grid-areas-layout 2xl:grid-cols-layout-large',
        'max-xl:mx-auto px-10 pb-10 gap-x-10'
      )}
    >
      <div
        className={cn(
          'flex flex-col items-center',
          'grid-in-content',
          '*:max-w-2xl *:xl:max-w-4xl *:py-10 mx-auto',
          'divide-y-2 divide-bg-border',
          'max-xl:[&>*:last-child]:!mb-10 max-xl:[&>*:last-child]:!border-b-2'
        )}
      >
        <Breadcrumb pages={services} />
        {children}
      </div>
      <CarouselSection services={services} clientFeedback={clientFeedback} projects={projects} />
    </div>
  )
}

export default GridLayout
