import ChallengeForm from '@/app/components/forms/ChallengeForm/ChallengeForm'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'
import ContactButton from '@/app/components/ContactButton'
import UnorderedList from '@/app/components/ui/UnorderedList'
import Blockquote from '@/app/components/ui/Blockquote'
import Heading from '@/app/components/ui/Heading'
import Strong from '@/app/components/ui/Strong'

const Roadblocks = () => {
  return (
    <>
      <div className="space-y-5 mb-10 pb-10 border-b-2">
        <Heading>Roadblocks on Every Step of the Way</Heading>
        <p>
          Taking that leap towards your dream app is super exciting. But, as you’ve probably found
          out, it comes with its own set of challenges.
        </p>
        <p>We guess you already checked off a few of them:</p>
        <UnorderedList>
          <li>
            ✔️ <Strong>Misfit Solutions</Strong>: When ready-made options just don’t capture your
            vision.
          </li>
          <li>
            ✔️ <Strong>The Code Mess</Strong>: When custom solutions become a jumbled mess of code
            that no man will ever decode again.
          </li>
          <li>
            ✔️ <Strong>Resource Drain</Strong>: When delays threaten both your time and budget.
          </li>
          <li>
            ✔️ <Strong>Momentum Loss</Strong>: When, without expert help, the initial spark of your
            project starts to wane.
          </li>
          <li>
            ✔️ <Strong>Your very own challenge</Strong>:{' '}
            <ContactButton variant="link">
              Write what bugs you most on the road to making your dream a reality here!
            </ContactButton>
          </li>
        </UnorderedList>
        <ContactButton FormComponent={ChallengeForm}>Add my challenge!</ContactButton>
        <p>
          And there are times when you probably wish your best friend was a full-stack application
          developer. You could call them up simply and start to work on your project right away -
          casually, in good company, just the way you need.
        </p>
        <Blockquote>Wait up! Isn’t this what Manystack is all about?</Blockquote>
      </div>
      <div className="space-y-5">
        <Heading>Here to Turn Your Roadblocks into Stepping Stones</Heading>
        <Blockquote>
          We turn your challenges into stepping stones towards building your dream app at Manystack.
        </Blockquote>
        <div>
          <Heading as="h3">
            🧩 Our <Strong>Modular Architecture</Strong> paired with{' '}
            <Strong>Component-Based Design</Strong>
          </Heading>
          <p>… allows your dream app to grow effortlessly with your changing needs.</p>
        </div>
        <div>
          <Heading as="h3">
            🧼 Our <Strong>Clean Code Principles</Strong>
          </Heading>
          <p>… ensure smooth sailing as your project evolves into your dream brought online.</p>
        </div>
        <div>
          <Heading as="h3">
            🚀 Our <Strong>Lean Development</Strong>
          </Heading>
          <p>… drives your launch into the market with breath-taking speed.</p>
        </div>
        <div>
          <Heading as="h3">
            🔄 Our <Strong>Agile Project Management</Strong>
          </Heading>
          <p>… keeps your creative energy alive and thriving.</p>
        </div>
        <Blockquote>
          Hand over your dream to our playful alliance forming. Let’s turn those challenges into
          amazing achievements, side by side together.
        </Blockquote>
        <ContactButton FormComponent={CooperationForm}>Drop a line to start!</ContactButton>
      </div>
    </>
  )
}

export default Roadblocks
