'use client'

import React from 'react'
import { Separator } from '@/components/ui/separator'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form as FormUI,
} from '@/components/ui/form'
import { Path, useForm } from 'react-hook-form'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { Checkbox } from '@/components/ui/checkbox'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { cn } from '@/lib/utils'

type Props<T extends z.ZodSchema> = {
  validationSchema: T
  defaultValues: z.infer<T>
  children: React.ReactNode
  onSubmit: (values: z.infer<T>) => Promise<void>
  header?: React.ReactNode
  submitButtonText?: string
}

const Form = <T extends z.ZodType>({
  validationSchema,
  defaultValues,
  header,
  children,
  onSubmit,
  submitButtonText = 'Submit',
}: Props<T>) => {
  const form = useForm<z.infer<typeof validationSchema>>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(validationSchema),
    defaultValues,
  })

  const {
    control,
    handleSubmit,
    clearErrors,
    reset,
    formState: { isSubmitting, defaultValues: formDefaultValues },
  } = form

  const hasAgreement = formDefaultValues && 'agreement' in formDefaultValues

  const handleFormSubmit = async (values: z.infer<typeof validationSchema>) => {
    await onSubmit(values)
    clearErrors()
    reset()
  }

  return (
    <>
      {header}
      {header && <Separator className="my-4" />}
      <FormUI {...form}>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8 m-0.5">
          {children}
          {hasAgreement && (
            <FormField
              control={control}
              name={'agreement' as Path<z.infer<typeof validationSchema>>}
              render={({ field }) => (
                <FormItem
                  className={cn(
                    'flex flex-row items-start',
                    'space-x-3 space-y-0 p-4',
                    'rounded-md border shadow'
                  )}
                >
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-4 leading-none">
                    <FormLabel className="!text-muted-foreground">
                      I appreciate that you only send me what matters and never share my info. I can
                      change my mind about access anytime.
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="animate-spin" />}
            {submitButtonText}
          </Button>
        </form>
      </FormUI>
    </>
  )
}

export default Form
