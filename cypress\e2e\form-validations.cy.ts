describe('Form Interactions and Validation', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000')
    // Intercept API calls to prevent actual form submissions during testing
    cy.intercept('POST', '/api/contact', {
      statusCode: 200,
      body: { message: 'Success' },
    }).as('contactSubmit')
    cy.intercept('POST', '/api/emailSubscription', {
      statusCode: 200,
      body: { message: 'Success' },
    }).as('emailSubscriptionSubmit')
    cy.intercept('POST', '/api/challenge', {
      statusCode: 200,
      body: { message: 'Success' },
    }).as('challengeSubmit')
    cy.intercept('POST', '/api/cooperation', {
      statusCode: 200,
      body: { message: 'Success' },
    }).as('cooperationSubmit')
  })

  describe('ContactForm Tests', () => {
    beforeEach(() => {
      cy.openForm('Your Story', "Let's Build It!")
    })

    it('should show validation errors for empty fields', () => {
      cy.submitForm()
      cy.expectValidationMessages(2)
    })

    it('should show validation error for invalid email', () => {
      cy.fillInput('email', 'invalid-email')
      cy.fillTextarea('message', 'Valid message text.')
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for short message', () => {
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Short')
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should successfully submit valid form and show thank you screen', () => {
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Long enough valid message.')
      cy.submitForm()
      cy.wait('@contactSubmit')
      cy.expectThankYouScreen()
    })
  })

  describe('EmailSubscriptionForm Tests', () => {
    beforeEach(() => {
      cy.openForm('Chart your concept!', 'Need a Spark of Inspiration?')
    })

    it('should show validation errors for empty fields', () => {
      cy.submitForm()
      cy.expectValidationMessages(4)
    })

    it('should show validation error for short name', () => {
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('additionalNotes', 'Valid message text.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for invalid email', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', 'invalid-email')
      cy.fillTextarea('additionalNotes', 'This is valid long text.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for short additional notes', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('additionalNotes', 'Short')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error when agreement checkbox is not checked', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('additionalNotes', 'Valid message with enough length.')
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should successfully submit valid form and show thank you screen', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('additionalNotes', 'Long enough message.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.wait('@emailSubscriptionSubmit')
      cy.expectThankYouScreen()
    })
  })

  describe('ChallengeForm Tests', () => {
    beforeEach(() => {
      cy.openForm('Add my challenge!', 'We Care About Your Toughest Challenges')
    })

    it('should show validation errors for empty fields', () => {
      cy.submitForm()
      cy.expectValidationMessages(4)
    })

    it('should show validation error for invalid email', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', 'invalid-email')
      cy.fillTextarea('yourChallenge', 'Valid challenge description.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for no name', () => {
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('yourChallenge', 'Valid challenge description.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error when "Your very own challenge" is selected but description is empty', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for short custom challenge description', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('yourChallenge', 'Short')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should disable/enable custom challenge textarea based on selection', () => {
      cy.isTextareaEnabled('yourChallenge', true)
      cy.selectRadioOption('misfit-solutions')
      cy.isRadioChecked('misfit-solutions')
      cy.isTextareaEnabled('yourChallenge', false)
      cy.selectRadioOption('own-challenge')
      cy.isRadioChecked('own-challenge')
      cy.isTextareaEnabled('yourChallenge', true)
    })

    it('should successfully submit with challenge description', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('yourChallenge', 'A well-described, lengthy challenge description.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.wait('@challengeSubmit')
      cy.expectThankYouScreen()
    })

    it('should successfully submit with predefined challenge option', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.checkCheckbox()
      cy.selectRadioOption('misfit-solutions')
      cy.submitForm()
      cy.wait('@challengeSubmit')
      cy.expectThankYouScreen()
    })
  })

  describe('CooperationForm Tests', () => {
    beforeEach(() => {
      cy.openForm('Drop a line to start!', 'Ready to Dream Big Together?')
    })

    it('should show validation errors for empty fields', () => {
      cy.submitForm()
      cy.expectValidationMessages(4)
    })

    it('should show validation error for invalid email', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', 'invalid-email')
      cy.fillTextarea('message', 'Valid message text.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error for short message', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Short')
      cy.checkCheckbox()
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should show validation error when agreement checkbox is not checked', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Valid message text.')
      cy.submitForm()
      cy.expectValidationMessages(1)
    })

    it('should successfully submit valid cooperation form', () => {
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Valid message text.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.wait('@cooperationSubmit')
      cy.expectThankYouScreen()
    })

    it('should successfully submit valid cooperation form with different project type', () => {
      cy.selectRadioOption('enhance-existing-systems')
      cy.fillInput('name', 'Test User')
      cy.fillInput('email', '<EMAIL>')
      cy.fillTextarea('message', 'Valid message text.')
      cy.checkCheckbox()
      cy.submitForm()
      cy.wait('@cooperationSubmit')
      cy.expectThankYouScreen()
    })
  })
})
