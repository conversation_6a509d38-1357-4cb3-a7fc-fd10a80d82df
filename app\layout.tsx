import './globals.css'
import type { Metadata } from 'next'
import { ReactNode } from 'react'
import { Inter } from 'next/font/google'
import ContactDialogProvider from '@/app/components/ContactDialogProvider'
import { GoogleAnalytics } from '@next/third-parties/google'
import { clientFeedback } from '@/app/data/clientFeedback'
import { projects } from '@/app/data/projects'
import { getPagesByFolder } from '@/lib/getPagesByFolder'
import GridLayout from '@/app/components/GridLayout'
import Footer from '@/app/components/Footer'

const interScript = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    template: '%s - manystack',
    default: 'manystack',
  },
  description: '',
}

const RootLayout = ({ children }: { children: ReactNode }) => {
  const services = getPagesByFolder()

  return (
    <html lang="en">
      <body className={interScript.className}>
        <ContactDialogProvider>
          <GridLayout services={services} clientFeedback={clientFeedback} projects={projects}>
            {children}
          </GridLayout>
          <Footer />
        </ContactDialogProvider>
      </body>
      <GoogleAnalytics gaId="G-4C57M55LN8" />
    </html>
  )
}

export default RootLayout
