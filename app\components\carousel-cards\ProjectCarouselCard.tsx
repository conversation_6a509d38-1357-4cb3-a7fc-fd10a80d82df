import { CarouselProps } from '@/components/ui/carousel'
import { orderBy } from 'lodash'
import { Project } from '@/app/types/types'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import ContactButton from '@/app/components/ContactButton'
import ProjectListItem from '@/app/components/ProjectListItem'
import DrawerOverlay from '@/app/components/DrawerOverlay'
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'

type Props = {
  projects: Project[]
  activeCarouselIndex: number
  plugins: CarouselProps['plugins']
  setApi: CarouselProps['setApi']
  stopAutoplay: () => void
  startAutoplay: () => void
}

const callToActions = (renderFirstParagraph: boolean = true) => (
  <div className="flex flex-col justify-center gap-7 !px-16 font-semibold">
    {renderFirstParagraph && <p>Ready to take the leap?</p>}
    <ContactButton>Drop a line to start!</ContactButton>
    <p>Need a little help to spark your excitement?</p>
    <ContactButton variant="rainbow" FormComponent={EmailSubscriptionForm}>
      Chart your concept!
    </ContactButton>
  </div>
)

const ProjectCarouselCard = ({
  projects,
  activeCarouselIndex,
  plugins,
  setApi,
  stopAutoplay,
  startAutoplay,
}: Props) => {
  return (
    <CarouselCard
      title="Dreams Already Crafted"
      className="w-full h-[56rem] xl:h-auto flex-1 [&>div]:pb-20"
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
      overlay={
        <DrawerOverlay
          cardOverlayLabel="Ready to take the leap?"
          closeCondition={activeCarouselIndex === projects.length}
        >
          {callToActions(false)}
        </DrawerOverlay>
      }
    >
      {orderBy(projects, 'order').map(({ id, title, description, thumbnail, slug }) => (
        <ProjectListItem
          key={id}
          title={title}
          description={description}
          thumbnail={thumbnail}
          slug={slug}
        />
      ))}
      {callToActions()}
    </CarouselCard>
  )
}

export default ProjectCarouselCard
