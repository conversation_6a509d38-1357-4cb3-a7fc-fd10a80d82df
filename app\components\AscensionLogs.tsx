import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'
import AscentionCarouselCard from '@/app/components/carousel-cards/AscentionCarouselCard'
import ContactButton from '@/app/components/ContactButton'
import UnorderedList from '@/app/components/ui/UnorderedList'
import Heading from '@/app/components/ui/Heading'

const AscensionLogs = () => {
  return (
    <div className="space-y-5">
      <Heading>Ascension Logs Across SaaS Niches</Heading>
      <p>
        Ever think your niche doesn’t need a cloud app? It’s time to dream bigger. Imagine a world
        where:
      </p>
      <UnorderedList>
        <li>Everyone has enough time.</li>
        <li>Money isn’t an issue.</li>
        <li>Every task is simple.</li>
        <li>All the insights have been discovered.</li>
        <li>Enjoyment is key.</li>
      </UnorderedList>
      <p>Impossible, right? Nearly every niche faces these challenges.</p>
      <p>
        In many fields, businesses tackle challenges by building SaaS apps, and many choose
        Manystack to take care of their lasting ascension into the cloud.
      </p>
      <AscentionCarouselCard />
      <p>Why not explore new SaaS adventures tailored for your dream project with Manystack?</p>
      <p>We can’t wait to discover your big idea and ascend it into the cloud.</p>
      <ContactButton FormComponent={CooperationForm}>Drop a line to start!</ContactButton>
      <p>
        Or, if you need more sparks to light up your thoughts, try out our simple SaaS
        implementation guide and{' '}
        <ContactButton variant="link" FormComponent={EmailSubscriptionForm}>
          chart your concept
        </ContactButton>{' '}
        before taking flight.
      </p>
    </div>
  )
}

export default AscensionLogs
