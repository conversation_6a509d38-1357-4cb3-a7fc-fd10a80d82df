'use client'

import { cn } from '@/lib/utils'
import { useState, ReactNode } from 'react'
import OverlayLayout from '@/app/components/OverlayLayout'

type Props = {
  children: ReactNode
  cardOverlayLabel: string
  link?: string
  closeCondition?: boolean
}

const DrawerOverlay = ({ children, cardOverlayLabel, link, closeCondition }: Props) => {
  const [isOverlayOpen, setIsOverlayOpen] = useState(false)

  return (
    <OverlayLayout
      cardOverlayLabel={cardOverlayLabel}
      link={link}
      className={cn('grid grid-rows-[0fr]', isOverlayOpen && 'grid-rows-[1fr]')}
      closeCondition={closeCondition}
      onClick={() => setIsOverlayOpen(!isOverlayOpen)}
      role="button"
      tabIndex={0}
      aria-expanded={isOverlayOpen}
      onKeyDown={event => {
        if (event.key === 'Enter') setIsOverlayOpen(!isOverlayOpen)
      }}
    >
      <div className="overflow-hidden *:pb-7">{children}</div>
    </OverlayLayout>
  )
}

export default DrawerOverlay
