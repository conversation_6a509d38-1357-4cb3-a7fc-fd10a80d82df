'use client'

import { <PERSON>actN<PERSON>, MouseEvent } from 'react'
import { FormComponent, useContactDialog } from '@/app/components/ContactDialogProvider'
import { Button } from '@/components/ui/button'
import { RainbowButton } from '@/components/ui/rainbow-button'
import ContactForm from '@/app/components/forms/ContactForm/ContactForm'
import { cn } from '@/lib/utils'

type Props = {
  children: ReactNode
  variant?: 'default' | 'rainbow' | 'link' | 'outline'
  FormComponent?: FormComponent
}

const ContactButton = ({ children, variant = 'default', FormComponent = ContactForm }: Props) => {
  const { openContactDialog } = useContactDialog()

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    openContactDialog(FormComponent)
  }

  switch (variant) {
    case 'rainbow':
      return <RainbowButton onClick={handleClick}>{children}</RainbowButton>
    case 'link':
      return (
        <Button
          onClick={handleClick}
          variant="link"
          className={cn(
            '!text-blue-500 underline underline-offset-auto',
            'whitespace-normal text-left p-0'
          )}
        >
          {children}
        </Button>
      )
    case 'outline':
      return (
        <Button onClick={handleClick} variant="outline">
          {children}
        </Button>
      )
    default:
      return <Button onClick={handleClick}>{children}</Button>
  }
}

export default ContactButton
