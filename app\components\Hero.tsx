import ContactButton from '@/app/components/ContactButton'
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'
import Heading from '@/app/components/ui/Heading'

const Hero = () => {
  return (
    <div className="flex flex-col justify-center text-center h-screen -mt-10 max-xl:border-b-2">
      <Heading as="h1" className="font-normal">
        manystack
      </Heading>
      <div className="flex flex-col items-center gap-8 text-gray-500">
        <p>Web and Mobile Applications for Success</p>
        <p>
          Our Dream is to build Your Dream.
          <br />
          With ❤️.
        </p>
        <p>
          Your dream project deserves a homely spot to come alive. Join us in a playful alliance:
          <br />
          give way to an app that leaves each one of your users in awe.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <ContactButton variant="rainbow" FormComponent={EmailSubscriptionForm}>
            Chart your concept!
          </ContactButton>
          <ContactButton variant="outline" FormComponent={CooperationForm}>
            Drop a line to start!
          </ContactButton>
        </div>
      </div>
    </div>
  )
}

export default Hero
