import { orderBy } from 'lodash'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import { CarouselProps } from '@/components/ui/carousel'
import { Feedback } from '@/app/types/types'
import ContactButton from '@/app/components/ContactButton'
import ClientFeedbackListItem from '@/app/components/ClientFeedbackListItem'
import OverlayLayout from '@/app/components/OverlayLayout'
import Heading from '@/app/components/ui/Heading'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'

type Props = {
  clientFeedback: Feedback[]
  plugins: CarouselProps['plugins']
  setApi: CarouselProps['setApi']
  stopAutoplay: () => void
  startAutoplay: () => void
}

const ClientFeedbackCarouselCard = ({
  clientFeedback,
  plugins,
  setApi,
  stopAutoplay,
  startAutoplay,
}: Props) => {
  const featuredClientFeedback = clientFeedback.filter(({ featured }) => featured)
  const firstCard = (
    <div className="flex flex-col gap-4 mx-8 *:lg:line-clamp-4 *:2xl:line-clamp-5">
      <Heading as="h3" className="text-base text-gray-700">
        Over nearly 20 years, we’ve had the joy of crafting online heartquarters for several
        dreambuilders—entrepreneurs whose visions blossomed into amazing apps.
      </Heading>
      <p className="text-gray-600">
        Here’s how a few of our cloud-settling partners experienced dreamcrafting with Manystack →
      </p>
    </div>
  )

  const lastCard = (
    <div className="flex flex-col gap-6 mx-8 *:lg:line-clamp-5">
      <Heading as="h3" className="text-base text-gray-700">
        Will you be the next cloudsettler, enjoying our playful alliance while building something
        remarkable for the future?
      </Heading>
      <ContactButton FormComponent={CooperationForm}>Drop a line to start!</ContactButton>
    </div>
  )

  return (
    <CarouselCard
      title="Building Dreams The Manystack Way"
      className="w-full h-auto flex-1 [&>div]:pb-14"
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
      overlay={<OverlayLayout cardOverlayLabel="See more!" link="/client-feedback" />}
    >
      {firstCard}
      {orderBy(featuredClientFeedback, 'order').map(({ id, text, note, featured, client }) => (
        <ClientFeedbackListItem
          key={id}
          text={text}
          note={note}
          client={client}
          featured={featured}
        />
      ))}
      {lastCard}
    </CarouselCard>
  )
}

export default ClientFeedbackCarouselCard
