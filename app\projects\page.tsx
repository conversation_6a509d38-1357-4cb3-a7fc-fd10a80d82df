import { Metadata } from 'next'
import Heading from '@/app/components/ui/Heading'
import { projects } from '@/app/data/projects'
import ProjectListItem from '@/app/components/ProjectListItem'

export const metadata: Metadata = {
  title: 'Projects',
}

const ProjectsPage = () => {
  return (
    <div className="flex flex-col justify-center gap-10">
      <Heading as="h1">Projects</Heading>
      <div className="grid grid-cols-1 gap-10">
        {projects.map(({ id, title, description, thumbnail, slug }) => (
          <article className="h-[48rem] rounded p-8 bg-gray-200" key={id}>
            <ProjectListItem
              title={title}
              description={description}
              thumbnail={thumbnail}
              slug={slug}
            />
          </article>
        ))}
      </div>
    </div>
  )
}

export default ProjectsPage
