import { Feedback } from '@/app/types/types'
import { cn } from '@/lib/utils'

type Props = Pick<Feedback, 'text' | 'note' | 'featured' | 'client'>

const ClientFeedbackListItem = ({
  text,
  note,
  featured,
  client: { name, title, company, website },
}: Props) => {
  return (
    <div className="flex gap-4 h-full">
      <span className="-mt-2 text-8xl text-gray-400">”</span>
      <div
        className={cn(
          'flex flex-col overflow-hidden mr-8',
          featured ? '*:lg:line-clamp-3 gap-4' : '*:lg:line-clamp-5 gap-10'
        )}
      >
        <blockquote className="text-gray-700" title={text}>
          {text}
        </blockquote>
        {note && (
          <p className="text-gray-600 my-auto" title={note}>
            *{note}*
          </p>
        )}
        <div className="mt-auto text-gray-600" rel="author">
          <div className="font-semibold">
            {name}
            {title && (
              <>
                {' '}
                - <span>{title}</span>
              </>
            )}
          </div>
          {company && <span className="block">{company}</span>}
          {website && (
            <a className="block underline truncate" href={website} target="_blank">
              {website}
            </a>
          )}
        </div>
      </div>
    </div>
  )
}

export default ClientFeedbackListItem
