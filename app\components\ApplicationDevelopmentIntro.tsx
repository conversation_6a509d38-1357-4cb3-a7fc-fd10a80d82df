import ContactButton from '@/app/components/ContactButton'
import CooperationForm from '@/app/components/forms/CooperationForm/CooperationForm'
import OrderedList from '@/app/components/ui/OrderedList'
import Heading from '@/app/components/ui/Heading'
import Strong from '@/app/components/ui/Strong'

const ApplicationDevelopmentIntro = () => {
  return (
    <div className="space-y-5">
      <Heading>Experience Application Development With Ease</Heading>
      <p>
        At Manystack, we recognize that you’re a dreambuilder—an entrepreneur set on bringing your
        major idea to life through an exceptional application. To make that happen, you need a
        development process that’s smooth and straightforward, turning your dreams into impressive
        apps that capture users’ hearts. But in a tech world that feels like a maze, it’s easy to
        feel stuck and unsure of the path ahead.
      </p>
      <p>
        Whether you want to build something new from scratch or complete/reshape/expand a current
        system, we don’t think it’s right that your creativity gets held back by confusing tech
        problems and developers unfit for the task. We know how exciting—and challenging—it is to
        bring a dream project to life. That’s why we’re all about Dreamcrafting with you. We thrive
        on blending creative insights and nimble strategies to materialize your online heartquarters
        through the power of React and Node.js. With our commitment to optimal performance and
        scalable infrastructure, we’re all about giving your dreams a strong, flexible base that’s
        ready to grow.
      </p>
      <p>Here’s how we do it with our lean agile methodology:</p>
      <OrderedList>
        <li>
          <Strong>Start and Plan Together</Strong>: First, we have a casual talk to get your app
          idea clear. We make a basic version known as a minimum viable product (MVP), to test with
          users. This helps us start strong and know what users like.
        </li>
        <li>
          <Strong>Build and Learn</Strong>: Next, we build your app in small steps. We use feedback
          from the MVP to guide us. This helps us stay focused on what users need and make changes
          quickly if needed.
        </li>
        <li>
          <Strong>Stay Flexible and Improve</Strong>: As we build each part, we keep things
          flexible. We put heads together with you often and use new ideas to keep the app growing.
          This way, the app turns out just right for you and your users.
        </li>
      </OrderedList>
      <p>
        Let’s start ascending your dream project into the cloud now. Move beyond feeling lost and
        present an app that’s the true reflection of your dream. It’s time to open the doors to your
        online heartquarters and step into your role as a true cloudsettler of your industry.
      </p>
      <ContactButton FormComponent={CooperationForm}>Drop a line to start!</ContactButton>
    </div>
  )
}

export default ApplicationDevelopmentIntro
